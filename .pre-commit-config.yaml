repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
      - id: trailing-whitespace
        exclude: tests
      - id: end-of-file-fixer
      - id: check-merge-conflict
      - id: check-case-conflict
      - id: check-json
      - id: check-toml
        exclude: tests/fixtures/invalid_lock/poetry\.lock
      - id: check-yaml
      - id: pretty-format-json
        args: [--autofix, --no-ensure-ascii, --no-sort-keys]
      - id: check-ast
      - id: debug-statements
      - id: check-docstring-first
  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.3.5
    hooks:
      - id: ruff
      - id: ruff-format
