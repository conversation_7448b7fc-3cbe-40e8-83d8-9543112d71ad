---
name: feature request
about: suggested feature
title: ''
labels: feature request 
assignees: ''
---

**What is the issue about?**
- [ ] improving/extending existing features;
- [ ] issue requires additional research to be implemented

#### For improving/extending existing features, please include:  
 - Description of the proposed feature. 
 - Include a short motivation of why the feature will be useful to users. 

**For feature proposals that require additional research, include:**
* Proposal and background.
* Motivation of why the feature is interesting from reserach perspective and why it will be useful for the users.
* Short outline of experiments, and description of necessary sub components (e.g. data curation, training, evaluation), if needed.
* Related literature or list of references that may be useful in evaluating the reserach proposal.

We will use github issues to the discuss the proposal. Based on our discussions, the contributor(s) will proceed to implement the feature and will provide guidance/help, as needed. 
Once we receive the PRs, we will review them and merge them after we perform necessary verificaiton and fixing any bugs.

