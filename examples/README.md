# Examples

This directory contains example scripts demonstrating the usage of the `translation-agent` workflow.

## Contents
- `example_script.py`: A simple script showing how to perform machine translation using the package.
- `sample-texts/`: A directory containing a few sample texts from The Batch letters written by <PERSON> and <PERSON> Points summaries found on the [DeepLearning.ai website](https://www.deeplearning.ai/the-batch/tag/data-points/).

## Usage
To run the example scripts, ensure that you have installed the `translation-agent` package and have activated your virtual environment. Then run:

```python
python example_script.py
```

If you have any questions or encounter any issues, please feel free to open an issue on the Github repository.
