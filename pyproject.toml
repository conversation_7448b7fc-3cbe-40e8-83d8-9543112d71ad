[tool.poetry]
name = "translation-agent"
version = "0.1.0"
description = "Agentic workflow for machine translation using LLMs"
authors = ["<PERSON> <<EMAIL>>"]
license = "MIT"
readme = "README.md"
package-mode = true
packages = [{ include = "translation_agent", from = "src" }]
repository = "https://github.com/andrewyng/translation-agent"
keywords = ["translation", "agents", "LLM", "machine translation"]


[tool.poetry.dependencies]
python = "^3.9"
openai = "^1.28.1"
tiktoken = "^0.6.0"
joblib = "^1.4.2"
pysrt = "^1.1.2"
icecream = "^2.1.3"
langchain-text-splitters = "^0.0.1"
python-dotenv = "^1.0.1"

[tool.poetry.group.app]
optional = true

[tool.poetry.group.app.dependencies]
simplemma = "^1.0.0"
gradio = "4.37.2"
python-docx = "^1.1.2"
PyMuPDF = "^1.24.7"

[tool.poetry.group.dev]
optional = true

[tool.poetry.group.dev.dependencies]
black = "^24.4.2"
flake8 = "^7.0.0"
pyright = "^1.1.362"
pre-commit = "^3.7.1"
ruff = "^0.4.4"

[tool.poetry.group.test]
optional = true

[tool.poetry.group.test.dependencies]
pytest = "^8.2.0"
mypy = "^1.10.0"
pytest-mock = "^3.14.0"

[tool.poetry.group.eval]
optional = true

[tool.poetry.group.eval.dependencies]
nltk = "^3.8.1"
sacrebleu = "^2.4.2"
google-cloud-translate = "^3.15.3"
deepl = "^1.18.0"
numpy = "^1.26.4"
scipy = "^1.13.0"
gradio = "^4.31.5"
requests = "^2.32.3"
beautifulsoup4 = "^4.12.3"
sentencepiece = "^0.2.0"


[[tool.poetry.source]]
name = "pytorch"
url = "https://download.pytorch.org/whl/nightly/rocm6.0"
priority = "supplemental"

[tool.ruff]
# Set the maximum line length to 79.
line-length = 79
indent-width = 4
exclude = [".venv", ".env", ".git", "tests", "eval", ".jj"]

[tool.ruff.lint]
# Add the `line-too-long` rule to the enforced rule set. By default, Ruff omits rules that
# overlap with the use of a formatter, like Black, but we can override this behavior by
# explicitly adding the rule.
extend-select = [
    "B",   # flake8-bugbear
    "C4",  # flake8-comprehensions
    "ERA", # flake8-eradicate/eradicate
    "I",   # isort
    "N",   # pep8-naming
    "PIE", # flake8-pie
    "PGH", # pygrep
    "RUF", # ruff checks
    "SIM", # flake8-simplify
    # "T20", # flake8-print
    "TCH", # flake8-type-checking
    "TID", # flake8-tidy-imports
    "UP",  # pyupgrade
]
fixable = ["ALL"]
ignore = ["SIM117"]

[tool.ruff.lint.isort]
force-single-line = false
lines-after-imports = 2
known-first-party = ["translation-agent"]

[tool.ruff.lint.per-file-ignores]
"**/__init__.py" = ["E402", "F401"]
"**/{tests,docs,tools}/*" = ["E402"]


[tool.mypy]
files = "src, tests"
mypy_path = "src"
namespace_packages = true
explicit_package_bases = true
show_error_codes = true
strict = true
enable_error_code = ["ignore-without-code", "redundant-expr", "truthy-bool"]
exclude = ["tests"]

[tool.ruff.format]
quote-style = "double"
indent-style = "space"
skip-magic-trailing-comma = false
line-ending = "auto"


[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
